import { useState, useRef, useEffect } from 'react';
import {
  Box,
  Card,
  Grid,
  Avatar,
  Typography,
  LinearProgress,
  IconButton,
  styled,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const SCROLL_SPEED = 400; // pixels to scroll per click

const CarouselArrowButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 9,
  width: 48,
  height: 32,
  borderRadius: '10%',
  backgroundColor: theme.palette.primary.main,
  boxShadow: theme.customShadows.z8,
  color: 'white',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
    color: 'primary.main',
  },
  [theme.breakpoints.down('sm')]: {
    width: 30,
    height: 30,
  },
}));

// ----------------------------------------------------------------------

type CloudPlatform = {
  id: string;
  name: string;
  icon: string;
  files: number;
  color: string;
  progress: number;
};

type CloudPlatformsCarouselProps = {
  platforms: CloudPlatform[];
};

export default function CloudPlatformsCarousel({ platforms }: CloudPlatformsCarouselProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);

  // Calculate maximum scroll width on mount and window resize
  useEffect(() => {
    const updateScrollInfo = () => {
      if (scrollRef.current) {
        const { scrollWidth, clientWidth } = scrollRef.current;
        setMaxScroll(scrollWidth - clientWidth);

        // Update arrow visibility based on current position
        setShowLeftArrow(scrollPosition > 0);
        setShowRightArrow(scrollPosition < scrollWidth - clientWidth - 10);
      }
    };

    updateScrollInfo();
    window.addEventListener('resize', updateScrollInfo);

    return () => {
      window.removeEventListener('resize', updateScrollInfo);
    };
  }, [scrollPosition]);

  const handleScrollLeft = () => {
    if (scrollRef.current) {
      const newPosition = Math.max(0, scrollPosition - SCROLL_SPEED);
      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth',
      });
      setScrollPosition(newPosition);
    }
  };

  const handleScrollRight = () => {
    if (scrollRef.current) {
      const newPosition = Math.min(maxScroll, scrollPosition + SCROLL_SPEED);
      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth',
      });
      setScrollPosition(newPosition);
    }
  };

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setScrollPosition(scrollLeft);
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  return (
    <Box sx={{ position: 'relative', mb: 4 }}>
      <CarouselArrowButton
        onClick={handleScrollLeft}
        sx={{
          left: { xs: -10, sm: -15, md: -18 },
          boxShadow: (theme) => theme.customShadows.z8,
          zIndex: 10,
          opacity: showLeftArrow ? 1 : 0.5,
          pointerEvents: showLeftArrow ? 'auto' : 'none',
        }}
        aria-label="previous platforms"
        disabled={!showLeftArrow}
      >
        <Iconify icon="eva:arrow-ios-back-fill" width={20} />
      </CarouselArrowButton>

      <CarouselArrowButton
        onClick={handleScrollRight}
        sx={{
          right: { xs: -10, sm: -15, md: -18 },
          boxShadow: (theme) => theme.customShadows.z8,
          zIndex: 10,
          opacity: showRightArrow ? 1 : 0.5,
          pointerEvents: showRightArrow ? 'auto' : 'none',
        }}
        aria-label="next platforms"
        disabled={!showRightArrow}
      >
        <Iconify icon="eva:arrow-ios-forward-fill" width={20} />
      </CarouselArrowButton>

      <Box
        ref={scrollRef}
        onScroll={handleScroll}
        sx={{
          display: 'flex',
          overflowX: 'auto',
          gap:1 ,
          py: 1,
          px: { xs: 2, sm: 3, md: 4 },
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none', // IE/Edge
          '&::-webkit-scrollbar': {
            // Chrome/Safari/Opera
            display: 'none',
          },
        }}
      >
        {platforms.map((platform) => (
          <Card
            key={platform.id}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              backgroundColor: 'rgba(242, 242, 242, 1)',
              alignItems: 'start',
              p: 2,
              boxShadow: 'none',
              border: '1px solid #f0f0f0',
              width: '18%',
              height: 150,
              flexShrink: 0,
              cursor: 'pointer',
              borderRadius: '8px',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: (theme) => theme.customShadows.z8,
                borderColor: 'primary.main',
              },
            }}
          >
            <Avatar
              src={platform.icon}
              sx={{ width: 44, height: 44, bgcolor: 'transparent' }}
            />
            <Typography variant="subtitle1" sx={{ my:0.5 }}>
              {platform.name}
            </Typography>
            <Typography color='gray' variant="caption" >
              {platform.files} files
            </Typography>
            
            <Box sx={{ width: '100%', mt: 'auto' }}>
              <LinearProgress
                variant="determinate"
                value={platform.progress}
                sx={{
                  height: 8,
                  borderRadius: 2,
                  '& .MuiLinearProgress-bar': {
                    bgcolor: 'primary.main',
                    borderRadius: 2,
                  },
                }}
              />
            </Box>
          </Card>
        ))}
      </Box>
    </Box>
  );
}
