import { useState, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Box,
  IconButton,
  styled,
  LinearProgress,
  Stack,
  alpha,
  useTheme,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Upload } from 'src/components/upload';
import { FileThumbnail } from 'src/components/file-thumbnail';

// ----------------------------------------------------------------------

const StyledDropZone = styled('div')(({ theme }) => ({
  outline: 'none',
  padding: theme.spacing(5, 1),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.neutral,
  border: `1px dashed ${theme.palette.divider}`,
  '&:hover': { opacity: 0.72, cursor: 'pointer' },
  ...(theme.palette.mode === 'dark' && {
    backgroundColor: alpha(theme.palette.common.white, 0.08),
    border: `1px dashed ${alpha(theme.palette.common.white, 0.23)}`,
  }),
}));

// ----------------------------------------------------------------------

type FileUploadDialogProps = {
  open: boolean;
  onClose: () => void;
};

export default function FileUploadDialog({ open, onClose }: FileUploadDialogProps) {
  const [files, setFiles] = useState<(File | string)[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleDropFiles = useCallback(
    (acceptedFiles: File[]) => {
      setFiles([...files, ...acceptedFiles]);
    },
    [files]
  );

  const handleRemoveFile = (inputFile: File | string) => {
    const filtered = files.filter((file) => file !== inputFile);
    setFiles(filtered);
  };

  const handleUploadFiles = () => {
    if (files.length === 0) return;

    setIsUploading(true);

    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      setUploadProgress(progress);

      if (progress >= 100) {
        clearInterval(interval);
        setIsUploading(false);
        // Reset after successful upload
        setTimeout(() => {
          setFiles([]);
          setUploadProgress(0);
          onClose();
        }, 500);
      }
    }, 200);
  };

  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  return (
    <Dialog
      open={open}
      onClose={!isUploading ? onClose : undefined}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          bgcolor: (theme) =>
            theme.palette.mode === 'dark'
              ? theme.palette.background.paper
              : theme.palette.common.white,
        },
      }}
    >
      <DialogTitle sx={{ position: 'relative', p: 3 }}>
        <Typography
          variant="h6"
          sx={{
            color: (theme) =>
              theme.palette.mode === 'dark'
                ? theme.palette.common.white
                : theme.palette.text.primary,
          }}
        >
          Upload Files
        </Typography>

        {!isUploading && (
          <IconButton
            onClick={onClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) =>
                theme.palette.mode === 'dark'
                  ? theme.palette.common.white
                  : theme.palette.grey[500],
            }}
          >
            <Iconify icon="eva:close-fill" />
          </IconButton>
        )}
      </DialogTitle>

      <DialogContent sx={{ px: 3 }}>
        {isUploading ? (
          <Box sx={{ width: '100%', mt: 2, mb: 3 }}>
            <Typography
              variant="body2"
              sx={{
                mb: 1,
                color: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.common.white
                    : theme.palette.text.primary,
              }}
            >
              Uploading... {uploadProgress}%
            </Typography>
            <LinearProgress
              variant="determinate"
              value={uploadProgress}
              sx={{
                height: 8,
                borderRadius: 4,
                bgcolor: (theme: any) => theme.palette.primary.main + '20',
                '& .MuiLinearProgress-bar': {
                  bgcolor: 'primary.main',
                  borderRadius: 4,
                },
              }}
            />
          </Box>
        ) : (
          <>
            <Typography
              variant="body2"
              sx={{
                mb: 3,
                color: (theme) =>
                  theme.palette.mode === 'dark'
                    ? alpha(theme.palette.common.white, 0.7)
                    : theme.palette.text.secondary,
              }}
            >
              Drag and drop files here or click to browse files from your computer
            </Typography>

            <Upload
              multiple
              files={files}
              onDrop={handleDropFiles}
              onRemove={handleRemoveFile}
              sx={{
                mb: 3,
                '& .MuiBox-root': {
                  bgcolor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.common.white, 0.08)
                      : theme.palette.background.neutral,
                  borderColor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.common.white, 0.23)
                      : theme.palette.divider,
                },
              }}
            />

            {files.length > 0 && (
              <Stack spacing={2} sx={{ mt: 2 }}>
                <Typography variant="subtitle2">{files.length} file(s) selected</Typography>

                <Box
                  gap={1}
                  display="grid"
                  gridTemplateColumns={{
                    xs: 'repeat(2, 1fr)',
                    sm: 'repeat(3, 1fr)',
                  }}
                >
                  {files.map((file) => {
                    const { name, size, preview } =
                      typeof file === 'string'
                        ? { name: file.split('/').pop() || '', size: 0, preview: file }
                        : { name: file.name, size: file.size, preview: URL.createObjectURL(file) };

                    return (
                      <Box
                        key={name}
                        sx={{
                          p: 1,
                          borderRadius: 1,
                          border: (theme) =>
                            theme.palette.mode === 'dark'
                              ? `solid 1px ${alpha(theme.palette.common.white, 0.23)}`
                              : `solid 1px ${theme.palette.divider}`,
                          position: 'relative',
                          bgcolor: (theme) =>
                            theme.palette.mode === 'dark'
                              ? alpha(theme.palette.common.white, 0.04)
                              : theme.palette.background.paper,
                        }}
                      >
                        <FileThumbnail
                          file={file}
                          sx={{
                            width: 64,
                            height: 64,
                            mx: 'auto',
                            '& .MuiBox-root': {
                              bgcolor: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? alpha(theme.palette.common.white, 0.08)
                                  : theme.palette.background.neutral,
                            },
                          }}
                          imageView
                        />

                        <IconButton
                          size="small"
                          onClick={() => handleRemoveFile(file)}
                          sx={{
                            top: 4,
                            right: 4,
                            position: 'absolute',
                            color: 'common.white',
                            bgcolor: (theme) =>
                              theme.palette.mode === 'dark'
                                ? alpha(theme.palette.common.black, 0.6)
                                : alpha(theme.palette.grey[900], 0.48),
                            '&:hover': {
                              bgcolor: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? alpha(theme.palette.common.black, 0.8)
                                  : alpha(theme.palette.grey[900], 0.72),
                            },
                          }}
                        >
                          <Iconify icon="eva:close-fill" width={16} />
                        </IconButton>

                        <Box sx={{ mt: 0.5 }}>
                          <Typography
                            variant="caption"
                            sx={{
                              display: 'block',
                              width: 1,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              color: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? theme.palette.common.white
                                  : theme.palette.text.primary,
                            }}
                          >
                            {name}
                          </Typography>

                          {typeof file !== 'string' && (
                            <Typography
                              variant="caption"
                              sx={{
                                color: (theme) =>
                                  theme.palette.mode === 'dark'
                                    ? alpha(theme.palette.common.white, 0.7)
                                    : theme.palette.text.secondary,
                              }}
                            >
                              {size < 1024
                                ? `${size} B`
                                : size < 1024 * 1024
                                  ? `${Math.round(size / 1024)} KB`
                                  : `${Math.round(size / 1024 / 1024)} MB`}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    );
                  })}
                </Box>
              </Stack>
            )}
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, justifyContent: 'flex-end' }}>
        {!isUploading && (
          <>
            <Button
              variant="outlined"
              color="inherit"
              onClick={onClose}
              sx={{
                borderRadius: 1,
                borderColor: (theme) =>
                  theme.palette.mode === 'dark'
                    ? alpha(theme.palette.common.white, 0.23)
                    : alpha(theme.palette.grey[500], 0.32),
                color: (theme) =>
                  theme.palette.mode === 'dark'
                    ? theme.palette.common.white
                    : theme.palette.text.primary,
              }}
            >
              Cancel
            </Button>

            <Button
              variant="contained"
              disabled={files.length === 0}
              onClick={handleUploadFiles}
              sx={{
                bgcolor: 'primary.main',
                '&:hover': { bgcolor: 'primary.dark' },
                borderRadius: 1,
                ml: 1,
                '&.Mui-disabled': {
                  bgcolor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.action.disabled, 0.3)
                      : alpha(theme.palette.action.disabled, 0.12),
                  color: (theme) => theme.palette.action.disabled,
                },
              }}
            >
              Upload
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  );
}
