import { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  Stack,
  Typography,
  TextField,
  IconButton,
  Avatar,
  Paper,
  Chip,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Button,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  avatar?: string;
}

interface ChatHistory {
  id: string;
  title: string;
  timestamp: string;
}

const MOCK_MESSAGES: Message[] = [
  {
    id: '1',
    content: 'Figma flows bold ipsum text italic. Create hand connection ellipse stroke invite duplicate rotate blur. Resizing duplicate boolean.',
    sender: 'agent',
    timestamp: new Date(Date.now() - 60000),
    avatar: '/assets/icons/ai-avatar.svg',
  },
  {
    id: '2',
    content: 'Vertical inspect effect library flatten strikethrough list export overflow outline. Figma vertical share background export outline align mask edit list. Asset strikethrough layout.',
    sender: 'agent',
    timestamp: new Date(Date.now() - 30000),
    avatar: '/assets/icons/ai-avatar.svg',
  },
];

const MOCK_CHAT_HISTORY: ChatHistory[] = [
  {
    id: '1',
    title: 'Shadow mask overflow pixel',
    timestamp: 'Just Now',
  },
  {
    id: '2',
    title: 'Horizontal frame figma component',
    timestamp: 'Just Now',
  },
  {
    id: '3',
    title: 'Arrow list undo inspect',
    timestamp: 'Just Now',
  },
  {
    id: '4',
    title: 'Duplicate subtract inspect rectangle',
    timestamp: 'Just Now',
  },
];

export function AgentsView() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [messages, setMessages] = useState<Message[]>(MOCK_MESSAGES);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [chatHistory] = useState<ChatHistory[]>(MOCK_CHAT_HISTORY);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = (content: string) => {
    if (!content.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: content.trim(),
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate agent response
    setTimeout(() => {
      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Thank you for your message! I\'m processing your request and will provide a detailed response shortly.',
        sender: 'agent',
        timestamp: new Date(),
        avatar: '/assets/icons/ai-avatar.svg',
      };
      setMessages((prev) => [...prev, agentMessage]);
      setIsTyping(false);
    }, 2000);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage(inputValue);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        width: '100%',
        background: (theme) => theme.palette.background.default,
        overflow: 'hidden',
      }}
    >
      {/* Main Chat Column */}
      <Card
        component="main"
        sx={{
          flexGrow: 1,
          m: { xs: 1, sm: 2 },
          width: { xs: '100%', md: '60%', lg: '50%' },
          minWidth: 0,
          display: 'flex',
          flexDirection: 'column',
          bgcolor: 'background.paper',
          borderRadius: { xs: 1, sm: 2 },
          border: '1px solid',
          borderColor: 'divider',
          order: { xs: 1, md: 0 },
        }}
      >
        {/* Chat header */}
        <Box
          sx={{
            py: { xs: 1, sm: 2 },
            px: { xs: 2, sm: 3 },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            background: (theme) =>
              theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.8)' : 'white',
            backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(8px)' : 'none'),
            borderBottom: '1px solid',
            borderColor: 'divider',
            position: 'relative',
            zIndex: 5,
            minHeight: { xs: 56, sm: 64 },
          }}
        >
          {/* Left side - Title */}
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
            <Typography
              variant={isMobile ? 'h6' : 'h5'}
              sx={{
                fontWeight: 'bold',
                color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              Code Generation
            </Typography>
          </Box>

          {/* Right side - Actions */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton size="small" sx={{ color: 'text.secondary' }}>
              <Iconify icon="eva:star-outline" width={20} />
            </IconButton>
            <IconButton size="small" sx={{ color: 'text.secondary' }}>
              <Iconify icon="eva:share-outline" width={20} />
            </IconButton>
            <IconButton size="small" sx={{ color: 'text.secondary' }}>
              <Iconify icon="eva:more-vertical-fill" width={20} />
            </IconButton>
          </Box>
        </Box>

      {/* Chat Messages */}
      <Card
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          border: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Box
          sx={{
            flex: 1,
            overflow: 'auto',
            p: 2,
            display: 'flex',
            flexDirection: 'column',
            gap: 2,
          }}
        >
          {messages.map((message) => (
            <Box
              key={message.id}
              sx={{
                display: 'flex',
                justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
                mb: 1,
              }}
            >
              <Stack
                direction={message.sender === 'user' ? 'row-reverse' : 'row'}
                spacing={1}
                alignItems="flex-start"
                sx={{ maxWidth: '70%' }}
              >
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: message.sender === 'user' ? 'primary.main' : 'grey.300',
                  }}
                  src={message.avatar}
                >
                  {message.sender === 'user' ? (
                    <Iconify icon="mdi:account" width={20} />
                  ) : (
                    <Iconify icon="material-symbols-light:robot-2-outline-sharp" width={20} />
                  )}
                </Avatar>
                <Paper
                  sx={{
                    p: 1.5,
                    bgcolor: message.sender === 'user' ? 'primary.main' : 'background.neutral',
                    color: message.sender === 'user' ? 'primary.contrastText' : 'text.primary',
                    borderRadius: 2,
                    maxWidth: '100%',
                  }}
                >
                  <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                    {message.content}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      display: 'block',
                      mt: 0.5,
                      opacity: 0.7,
                      fontSize: '0.7rem',
                    }}
                  >
                    {message.timestamp.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </Typography>
                </Paper>
              </Stack>
            </Box>
          ))}

          {/* Typing Indicator */}
          {isTyping && (
            <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 1 }}>
              <Stack direction="row" spacing={1} alignItems="flex-start" sx={{ maxWidth: '70%' }}>
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'grey.300',
                  }}
                >
                  <Iconify icon="material-symbols-light:robot-2-outline-sharp" width={20} />
                </Avatar>
                <Paper
                  sx={{
                    p: 1.5,
                    bgcolor: 'background.neutral',
                    borderRadius: 2,
                  }}
                >
                  <Typography variant="body2" color="text.secondary">
                    AI is typing...
                  </Typography>
                </Paper>
              </Stack>
            </Box>
          )}

          <div ref={messagesEndRef} />
        </Box>

        {/* Message Input */}
        <Box
          sx={{
            p: 2,
            borderTop: '1px solid',
            borderColor: 'divider',
            bgcolor: 'background.paper',
          }}
        >
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type your message..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isTyping}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => handleSendMessage(inputValue)}
                    disabled={!inputValue.trim() || isTyping}
                    sx={{
                      bgcolor: 'primary.main',
                      color: 'primary.contrastText',
                      '&:hover': {
                        bgcolor: 'primary.dark',
                      },
                      '&.Mui-disabled': {
                        bgcolor: 'action.disabledBackground',
                        color: 'action.disabled',
                      },
                    }}
                  >
                    <Iconify icon="eva:paper-plane-fill" width={20} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
              },
            }}
          />
        </Box>
      </Card>
    </Box>
  );
}
