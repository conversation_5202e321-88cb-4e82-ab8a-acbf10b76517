import { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>ield,
  Avatar,
  Chip,
  InputAdornment,
  useTheme,
  Grid,
  IconButton,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { useRouter } from 'src/routes/hooks';


// ----------------------------------------------------------------------

interface AgentTemplate {
  id: string;
  name: string;
  type: 'Agent';
  description: string;
  category: string;
  status: 'active' | 'inactive';
  icon: string;
  iconColor: string;
}

const AGENT_TEMPLATES: AgentTemplate[] = [
  {
    id: '1',
    name: 'Telegram AI',
    type: 'Agent',
    description: 'Elevate your online presence with our Telegram agents. Schedule messages, analyze user engagement, and automate responses.',
    category: 'Social Media',
    status: 'active',
    icon: 'logos:telegram',
    iconColor: '#0088cc',
  },
  {
    id: '2',
    name: 'AI',
    type: 'Agent',
    description: 'Transform your project management with our Asana agents. Automate task assignments, track progress, and streamline workflows.',
    category: 'Marketing',
    status: 'active',
    icon: 'logos:asana',
    iconColor: '#f06a6a',
  },
  {
    id: '3',
    name: 'Gmail forward messages',
    type: 'Agent',
    description: 'Boost your email marketing with our Gmail agents. Automate campaigns, monitor responses, and manage your inbox efficiently.',
    category: 'Marketing',
    status: 'active',
    icon: 'logos:google-gmail',
    iconColor: '#ea4335',
  },
  {
    id: '4',
    name: 'Invoices',
    type: 'Agent',
    description: 'Streamline your payment processes with our Mastercard agents. Automate transactions, track payments, and manage billing.',
    category: 'Sales',
    status: 'active',
    icon: 'logos:mastercard',
    iconColor: '#eb001b',
  },
  {
    id: '5',
    name: 'Blog writer',
    type: 'Agent',
    description: 'Amplify your content reach with our Medium agents. Schedule posts, analyze readership, and optimize your publishing strategy.',
    category: 'Marketing',
    status: 'active',
    icon: 'simple-icons:medium',
    iconColor: '#000000',
  },
  {
    id: '6',
    name: 'Figma Enhancer',
    type: 'Agent',
    description: 'Enhance your design workflows with our Figma agents. Automate design reviews, track feedback, and streamline collaboration.',
    category: 'Marketing',
    status: 'active',
    icon: 'logos:figma',
    iconColor: '#f24e1e',
  },
];

// Filter options
const STATUS_FILTERS = ['All', 'Active (6)', 'Inactive'];
const CATEGORY_FILTERS = ['All', 'Social Media', 'Marketing', 'Sales'];

// Agent Card Component
const AgentCard = ({ agent }: { agent: AgentTemplate }) => {
  const theme = useTheme();
  const router = useRouter();

  const handleUseTemplate = () => {
    // Navigate to the conversation chat page
    router.push('/dashboard/agents/chat');
  };

  return (
    <Card
      sx={{
        p: 3,
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        background: (theme) =>
          `linear-gradient(145deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
        backdropFilter: 'blur(10px)',
        borderRadius: 3,
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: (theme) =>
            `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 50%, ${theme.palette.error.main} 100%)`,
          opacity: 0,
          transition: 'opacity 0.3s ease',
        },
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: (theme) => `${theme.shadows[8]}, 0 0 0 1px ${theme.palette.primary.main}20`,
          borderColor: 'primary.main',
          '&::before': {
            opacity: 1,
          },
        },
      }}
    >
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600} color="text.primary">
              {agent.name}
            </Typography>
            <Chip
              label={agent.type}
              size="small"
              sx={{
                bgcolor:(theme) => theme.palette.primary.light ,
                fontWeight: 600,
                fontSize: '0.75rem',
                letterSpacing: '0.025em',
                border: (theme) => `1px solid ${theme.palette.primary.main}40`,
                borderRadius: '6px',
                height: '24px',
                '& .MuiChip-label': {
                  px: 1.5,
                },
              }}
            />
          </Box>
          <IconButton
            size="small"
            sx={{
              color: 'text.secondary',
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: (theme) => theme.palette.primary.main + '10',
                color: 'primary.main',
                transform: 'scale(1.05)',
              },
            }}
          >
            <Icon icon="eva:more-horizontal-fill" width={18} height={18} />
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.4 }}>
          {agent.description}
        </Typography>

       <Box display="flex" alignItems="center" gap={1} mb={3}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      bgcolor: 'action.hover',
                      border: '1px solid',
                      borderColor: 'divider',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        transform: 'scale(1.1)',
                        bgcolor: 'action.selected',
                        borderColor: 'primary.main',
                      },
                    }}
                  >
                    <Icon icon={agent.icon} width={20} height={20} />
                  </Avatar>
                
                 
              </Box>

        <AppButton
          variant="outlined"
          color="primary"
          fullWidth
          onClick={handleUseTemplate}
          label=" Use Template"
        />
      </CardContent>
    </Card>
  );
};

export function AgentsView() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<AgentTemplate[]>(AGENT_TEMPLATES);
  const [selectedStatusFilter, setSelectedStatusFilter] = useState('All');
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState('All');

  // Filter agents based on search, status, and category
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterAgents(query, selectedStatusFilter, selectedCategoryFilter);
  };

  const handleStatusFilter = (status: string) => {
    setSelectedStatusFilter(status);
    filterAgents(searchQuery, status, selectedCategoryFilter);
  };

  const handleCategoryFilter = (category: string) => {
    setSelectedCategoryFilter(category);
    filterAgents(searchQuery, selectedStatusFilter, category);
  };

  const filterAgents = (query: string, statusFilter: string, categoryFilter: string) => {
    let filtered = AGENT_TEMPLATES;

    // Filter by search query
    if (query) {
      filtered = filtered.filter(
        (agent) =>
          agent.name.toLowerCase().includes(query.toLowerCase()) ||
          agent.description.toLowerCase().includes(query.toLowerCase()) ||
          agent.category.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'All') {
      if (statusFilter.includes('Active')) {
        filtered = filtered.filter((agent) => agent.status === 'active');
      } else if (statusFilter === 'Inactive') {
        filtered = filtered.filter((agent) => agent.status === 'inactive');
      }
    }

    // Filter by category
    if (categoryFilter !== 'All') {
      filtered = filtered.filter((agent) => agent.category === categoryFilter);
    }

    setFilteredAgents(filtered);
  };

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        p: { xs: 2, sm: 3, md: 4 },
        maxWidth: '1400px',
        mx: 'auto',
      }}
    >
      {/* Header Section */}
      <Box sx={{ mb: 5 }}>
        <Typography
          variant="h3"
          fontWeight={700}
          color="text.primary"
          sx={{
            mb: 1,
            fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' }
          }}
        >
          Agents
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            mb: 3,
            fontSize: '1.1rem',
            opacity: 0.8
          }}
        >
          Enable advanced workflows with applications
        </Typography>
      </Box>

      {/* Agents Templates Section */}
      <Box sx={{ mb: 6 }}>
        <Typography
          variant="h4"
          fontWeight={700}
          color="text.primary"
          sx={{
            mb: 4,
            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' }
          }}
        >
          Agents&apos; Templates
        </Typography>

        {/* Filter Section */}
        <Box sx={{ mb: 6, width: '100%' }}>
          {/* Status Filter Buttons - Above Search */}
          <Box sx={{ mb: 3, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {STATUS_FILTERS.map((filter) => (
              <Chip
                key={filter}
                label={filter}
                onClick={() => handleStatusFilter(filter)}
                variant={selectedStatusFilter === filter ? 'filled' : 'outlined'}
                sx={{
                  borderRadius: '20px',
                  height: 36,
                  px: 2,
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  bgcolor: selectedStatusFilter === filter ? 'primary.main' : 'transparent',
                  color: selectedStatusFilter === filter ? 'primary.contrastText' : 'text.primary',
                  borderColor: selectedStatusFilter === filter ? 'primary.main' : 'divider',
                  '&:hover': {
                    bgcolor: selectedStatusFilter === filter ? 'primary.dark' : 'action.hover',
                    borderColor: 'primary.main',
                  },
                }}
              />
            ))}
          </Box>

          {/* Search Bar */}
          <Box sx={{ mb: 3, width: '100%' }}>
            <TextField
              fullWidth
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:search-fill" width={20} sx={{ color: 'text.disabled' }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                width: '100%',
                '& .MuiOutlinedInput-root': {
                  borderRadius: 3,
                  bgcolor: 'background.paper',
                  height: 48,
                  fontSize: '0.95rem',
                  '&:hover': {
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                    },
                  },
                  '&.Mui-focused': {
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                    },
                  },
                },
              }}
            />
          </Box>

          {/* Category Filter Buttons - Below Search */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {CATEGORY_FILTERS.map((filter) => (
              <Chip
                key={filter}
                label={filter}
                onClick={() => handleCategoryFilter(filter)}
                variant={selectedCategoryFilter === filter ? 'filled' : 'outlined'}
                sx={{
                  borderRadius: '20px',
                  height: 36,
                  px: 2,
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  bgcolor: selectedCategoryFilter === filter ? 'primary.main' : 'transparent',
                  color: selectedCategoryFilter === filter ? 'primary.contrastText' : 'text.primary',
                  borderColor: selectedCategoryFilter === filter ? 'primary.main' : 'divider',
                  '&:hover': {
                    bgcolor: selectedCategoryFilter === filter ? 'primary.dark' : 'action.hover',
                    borderColor: 'primary.main',
                  },
                }}
              />
            ))}
          </Box>
        </Box>



        {/* Agents Grid */}
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          {filteredAgents.map((agent) => (
            <Grid item xs={12} sm={6} lg={4} key={agent.id}>
              <AgentCard agent={agent} />
            </Grid>
          ))}
        </Grid>

        {/* No Results */}
        {filteredAgents.length === 0 && (
          <Box
            sx={{
              textAlign: 'center',
              py: 12,
              px: 4,
            }}
          >
            <Box
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 3,
                borderRadius: '50%',
                bgcolor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Iconify
                icon="eva:search-outline"
                width={48}
                height={48}
                sx={{ color: 'text.disabled' }}
              />
            </Box>
            <Typography variant="h5" color="text.primary" sx={{ mb: 2, fontWeight: 600 }}>
              No agents found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
              Try adjusting your search or filter criteria to find the agents you&apos;re looking for
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}
