import { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  Stack,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>ield,
  Avatar,
  Chip,
  InputAdornment,
  useTheme,
  Grid,
  Tab,
  Tabs,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

interface AgentTemplate {
  id: string;
  name: string;
  type: 'Agent';
  description: string;
  category: string;
  status: 'active' | 'inactive';
  icon: string;
  iconColor: string;
}

const AGENT_TEMPLATES: AgentTemplate[] = [
  {
    id: '1',
    name: 'Telegram AI',
    type: 'Agent',
    description: 'Elevate your online presence with our Telegram agents. Schedule messages, analyze user engagement, and automate responses.',
    category: 'Social Media',
    status: 'active',
    icon: 'logos:telegram',
    iconColor: '#0088cc',
  },
  {
    id: '2',
    name: 'AI',
    type: 'Agent',
    description: 'Transform your project management with our Asana agents. Automate task assignments, track progress, and streamline workflows.',
    category: 'Marketing',
    status: 'active',
    icon: 'logos:asana',
    iconColor: '#f06a6a',
  },
  {
    id: '3',
    name: 'Gmail forward messages',
    type: 'Agent',
    description: 'Boost your email marketing with our Gmail agents. Automate campaigns, monitor responses, and manage your inbox efficiently.',
    category: 'Marketing',
    status: 'active',
    icon: 'logos:google-gmail',
    iconColor: '#ea4335',
  },
  {
    id: '4',
    name: 'Invoices',
    type: 'Agent',
    description: 'Streamline your payment processes with our Mastercard agents. Automate transactions, track payments, and manage billing.',
    category: 'Sales',
    status: 'active',
    icon: 'logos:mastercard',
    iconColor: '#eb001b',
  },
  {
    id: '5',
    name: 'Blog writer',
    type: 'Agent',
    description: 'Amplify your content reach with our Medium agents. Schedule posts, analyze readership, and optimize your publishing strategy.',
    category: 'Marketing',
    status: 'active',
    icon: 'simple-icons:medium',
    iconColor: '#000000',
  },
  {
    id: '6',
    name: 'Figma Enhancer',
    type: 'Agent',
    description: 'Enhance your design workflows with our Figma agents. Automate design reviews, track feedback, and streamline collaboration.',
    category: 'Marketing',
    status: 'active',
    icon: 'logos:figma',
    iconColor: '#f24e1e',
  },
];

const CATEGORIES = ['All', 'Active (6)', 'Inactive'];

// Agent Card Component
const AgentCard = ({ agent }: { agent: AgentTemplate }) => {
  const theme = useTheme();

  const handleUseTemplate = () => {
    // TODO: Implement navigation to agent setup
    console.log('Using template:', agent.name);
  };

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 3,
        bgcolor: 'background.paper',
        overflow: 'hidden',
        '&:hover': {
          transform: 'translateY(-8px)',
          boxShadow: theme.shadows[12],
          borderColor: 'primary.main',
          '& .agent-icon': {
            transform: 'scale(1.1)',
          },
        },
      }}
    >
      <CardContent sx={{ flex: 1, p: 3, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 3 }}>
          <Avatar
            className="agent-icon"
            sx={{
              width: 56,
              height: 56,
              bgcolor: 'transparent',
              border: '2px solid',
              borderColor: 'divider',
              borderRadius: 2,
              transition: 'all 0.3s ease',
            }}
          >
            <Icon icon={agent.icon} width={36} height={36} color={agent.iconColor} />
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              variant="h6"
              fontWeight={700}
              color="text.primary"
              sx={{
                mb: 1,
                fontSize: '1.1rem',
                lineHeight: 1.3,
              }}
            >
              {agent.name}
            </Typography>
            <Chip
              label={agent.type}
              size="small"
              sx={{
                bgcolor: 'primary.main',
                color: 'primary.contrastText',
                fontWeight: 600,
                fontSize: '0.75rem',
                height: 24,
                borderRadius: 1.5,
              }}
            />
          </Box>
        </Box>

        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            mb: 3,
            lineHeight: 1.6,
            fontSize: '0.875rem',
            display: '-webkit-box',
            WebkitLineClamp: 3,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            flex: 1,
          }}
        >
          {agent.description}
        </Typography>

        <AppButton
          variant="outlined"
          color="primary"
          fullWidth
          onClick={handleUseTemplate}
          label="Use Template"
          sx={{
            mt: 'auto',
            borderRadius: 2.5,
            textTransform: 'none',
            fontWeight: 600,
            height: 44,
            fontSize: '0.875rem',
            border: '2px solid',
            borderColor: 'primary.main',
            color: 'primary.main',
            '&:hover': {
              bgcolor: 'primary.main',
              color: 'primary.contrastText',
              transform: 'translateY(-1px)',
              boxShadow: theme.shadows[4],
            },
          }}
        />
      </CardContent>
    </Card>
  );
};

export function AgentsView() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(0);
  const [filteredAgents, setFilteredAgents] = useState<AgentTemplate[]>(AGENT_TEMPLATES);

  // Filter agents based on search and category
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterAgents(query, selectedCategory);
  };

  const handleCategoryChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategory(newValue);
    filterAgents(searchQuery, newValue);
  };

  const filterAgents = (query: string, categoryIndex: number) => {
    let filtered = AGENT_TEMPLATES;

    // Filter by search query
    if (query) {
      filtered = filtered.filter(
        (agent) =>
          agent.name.toLowerCase().includes(query.toLowerCase()) ||
          agent.description.toLowerCase().includes(query.toLowerCase()) ||
          agent.category.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filter by category
    const category = CATEGORIES[categoryIndex];
    if (category !== 'All') {
      if (category.startsWith('Active')) {
        filtered = filtered.filter((agent) => agent.status === 'active');
      } else if (category === 'Inactive') {
        filtered = filtered.filter((agent) => agent.status === 'inactive');
      } else {
        filtered = filtered.filter((agent) => agent.category === category);
      }
    }

    setFilteredAgents(filtered);
  };

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        p: { xs: 2, sm: 3, md: 4 },
        maxWidth: '1400px',
        mx: 'auto',
      }}
    >
      {/* Header Section */}
      <Box sx={{ mb: 5 }}>
        <Typography
          variant="h3"
          fontWeight={700}
          color="text.primary"
          sx={{
            mb: 1,
            fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' }
          }}
        >
          Agents
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            mb: 3,
            fontSize: '1.1rem',
            opacity: 0.8
          }}
        >
          Enable advanced workflows with applications
        </Typography>
      </Box>

      {/* Agents Templates Section */}
      <Box sx={{ mb: 6 }}>
        <Typography
          variant="h4"
          fontWeight={700}
          color="text.primary"
          sx={{
            mb: 4,
            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' }
          }}
        >
          Agents&apos; Templates
        </Typography>

        {/* Category Tabs */}
        <Box sx={{ mb: 4 }}>
          <Tabs
            value={selectedCategory}
            onChange={handleCategoryChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 500,
                minHeight: 48,
                px: 3,
                py: 1.5,
                color: 'text.secondary',
                borderRadius: 2,
                bgcolor: 'red',
                mr: 1,
                minWidth: 'auto',
                '&.Mui-selected': {
                  color: 'primary.main',
                  fontWeight: 600,
                  bgcolor: 'white',
                },
                '&:hover': {
                  bgcolor: 'action.hover',
                },
              },
              '& .MuiTabs-indicator': {
                display: 'none',
              },
              '& .MuiTabs-flexContainer': {
                gap: 1,
              },
            }}
          >
            {CATEGORIES.map((category, index) => (
              <Tab key={index} label={category} />
            ))}
          </Tabs>
        </Box>

        {/* Search Bar */}
        <Box sx={{ mb: 4 }}>
          <TextField
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" width={20} sx={{ color: 'text.disabled' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              maxWidth: 500,
              '& .MuiOutlinedInput-root': {
                borderRadius: 3,
                bgcolor: 'background.paper',
                height: 48,
                fontSize: '0.95rem',
                '&:hover': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                  },
                },
                '&.Mui-focused': {
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'primary.main',
                    borderWidth: 2,
                  },
                },
              },
            }}
          />
        </Box>

        {/* Category Filter Chips */}
        <Box sx={{ mb: 5 }}>
          <Stack direction="row" spacing={1.5} flexWrap="wrap" useFlexGap>
            {['All', 'Social Media', 'Marketing', 'Sales'].map((category, index) => (
              <Chip
                key={category}
                label={category}
                onClick={() => handleCategoryChange({} as React.SyntheticEvent, index === 0 ? 0 : index + 2)}
                sx={{
                  bgcolor: index === 0 ? 'primary.main' : 'background.paper',
                  color: index === 0 ? 'primary.contrastText' : 'text.primary',
                  borderRadius: 3,
                  fontWeight: 500,
                  px: 2,
                  py: 0.5,
                  height: 36,
                  fontSize: '0.875rem',
                  border: '1px solid',
                  borderColor: index === 0 ? 'primary.main' : 'divider',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    bgcolor: index === 0 ? 'primary.dark' : 'primary.lighter',
                    borderColor: 'primary.main',
                    transform: 'translateY(-1px)',
                  },
                }}
              />
            ))}
          </Stack>
        </Box>

        {/* Agents Grid */}
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          {filteredAgents.map((agent) => (
            <Grid item xs={12} sm={6} lg={4} key={agent.id}>
              <AgentCard agent={agent} />
            </Grid>
          ))}
        </Grid>

        {/* No Results */}
        {filteredAgents.length === 0 && (
          <Box
            sx={{
              textAlign: 'center',
              py: 12,
              px: 4,
            }}
          >
            <Box
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 3,
                borderRadius: '50%',
                bgcolor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Iconify
                icon="eva:search-outline"
                width={48}
                height={48}
                sx={{ color: 'text.disabled' }}
              />
            </Box>
            <Typography variant="h5" color="text.primary" sx={{ mb: 2, fontWeight: 600 }}>
              No agents found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
              Try adjusting your search or filter criteria to find the agents you&apos;re looking for
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}
