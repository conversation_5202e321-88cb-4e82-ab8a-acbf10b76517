import axios from 'axios';

import { CONFIG } from 'src/config-global';
import { paths } from 'src/routes/paths';
import { localStorageGetItem } from './storage-available';

// ----------------------------------------------------------------------

// Callback for handling 401 errors - to be set by auth context
let onUnauthorizedCallback: (() => void) | null = null;

export const setUnauthorizedCallback = (callback: (() => void) | null) => {
  onUnauthorizedCallback = callback;
};

const axiosInstance = axios.create({
  baseURL: CONFIG.site.WorkforcesServerUrl,
  withCredentials: true,
});

axiosInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem('jwt_access_token');

  config.headers.Authorization = `Bearer ${token}`;
  config.headers['accept-language'] = localStorageGetItem('i18nextLng');

  return config;
});
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.log('erorr', error);
    const originalRequest = error.config;
    if (
      error.response &&
      error.response.status === 401 &&
      originalRequest &&
      !originalRequest.url?.includes(endpoints.auth.refreshToken)
    ) {
      onUnauthorizedCallback?.();

      return axiosInstance(originalRequest);
    }

    // Always throw the backend error message (or fallback)
    const errorMessage =
      error.response?.data?.message || error.response?.statusText || 'Something went wrong!';

    return Promise.reject(new Error(errorMessage));
  }
);

export default axiosInstance;

// ----------------------------------------------------------------------

// ----------------------------------------------------------------------

export const endpoints = {
  auth: {
    signIn: '/auth/login/admin',
    signUp: '/auth/register',
    signOut: '/auth/logout',
    refreshToken: '/auth/refresh-token',
    forgetPassword: '/auth/request-password-change',
    resetPassword: '/auth/reset-password',
    exchangeOAuthCode: '/auth/login/exchange-code',
  },
  user: {
    me: '/users/me',
  },
};
