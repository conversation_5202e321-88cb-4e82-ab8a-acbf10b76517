import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Field } from 'src/components/hook-form/fields';

import { _account } from 'src/layouts/config-nav-account';
import { _notifications } from 'src/_mock/_notifications';
import { AccountMenu } from 'src/layouts/components/account-menu';
import { NotificationsMenu } from 'src/layouts/components/notifications-menu';
import { TeamMembersDialog } from 'src/sections/teams/chat/team-members-dialog';

import {
  Typography,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  IconButton,
  useTheme,
  useMediaQuery,
  InputBase,
  Paper,
  Avatar,
  AvatarGroup,
  Button,
  TextField,
  alpha,
  Card,
  Tab,
  Tabs,
  Menu,
  MenuItem,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  InputAdornment,
} from '@mui/material';

// import { AppContainer } from 'src/components/common';
import { TeamChat } from 'src/sections/teams/chat/team-chat';
import { useTeamChat } from 'src/sections/teams/chat/use-team-chat';
import { Iconify } from 'src/components/iconify';
import { Logo } from 'src/components/logo';
import { AppButton } from 'src/components/common';
import { CONFIG } from 'src/config-global';

// ----------------------------------------------------------------------

// Mock data for previous conversations
interface Conversation {
  id: string;
  title: string;
  name: string;
  timestamp: Date;
  isFavorite: boolean;
}

const MOCK_CONVERSATIONS: Conversation[] = [
  {
    id: 'conv1',
    title: 'Horizontal frame figjam component',
    name: 'New chat',
    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    isFavorite: false,
  },
  {
    id: 'conv2',
    title: 'Arrow list undo inspect',
    name: 'UI/UX explained for beginners',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
    isFavorite: true,
  },
];

// Mock team members
interface TeamMember {
  id: string;
  name: string;
  avatar: string;
}

const MOCK_TEAM_MEMBERS: TeamMember[] = [
  {
    id: 'member1',
    name: 'John Doe',
    avatar: '/assets/images/avatars/avatar_1.jpg',
  },
  {
    id: 'member2',
    name: 'Jane Smith',
    avatar: '/assets/images/avatars/avatar_2.jpg',
  },
  {
    id: 'member3',
    name: 'Mike Johnson',
    avatar: '/assets/images/avatars/avatar_3.jpg',
  },
  {
    id: 'member4',
    name: 'Sarah Williams',
    avatar: '/assets/images/avatars/avatar_4.jpg',
  },
];

export default function AgentsChatPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  const isSmallMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t } = useTranslation();

  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [teamName, setTeamName] = useState('AI Team');
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [conversations, setConversations] = useState<Conversation[]>(MOCK_CONVERSATIONS);
  const [credits] = useState(1462); // Credits are currently read-only
  const [inputValue, setInputValue] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [membersDialogOpen, setMembersDialogOpen] = useState(false);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [emailInput, setEmailInput] = useState('');
  const [sharedEmails, setSharedEmails] = useState<string[]>([]);
  const [currentChatId, setCurrentChatId] = useState<string | null>(id || null);
  const [currentChatFavorite, setCurrentChatFavorite] = useState<boolean>(false);

  const { messages, isTyping, handleSendMessage, teamMembers, clearMessages } =
    useTeamChat(teamName);

  // Notification handlers
  const handleMarkAllAsRead = () => {
    console.log('Chat page: Marking all notifications as read');
    // In a real app, you would call an API to mark all notifications as read
  };

  const handleViewAllNotifications = () => {
    console.log('Chat page: Navigate to all notifications page');
    // In a real app, you would navigate to the notifications page
  };

  // Fetch team details (in a real app, this would be an API call)
  useEffect(() => {
    // Simulate API call to get team details
    const fetchTeamDetails = async () => {
      // Try to get the team name from session storage first
      const storedTeamName = sessionStorage.getItem('teamName');

      if (storedTeamName) {
        setTeamName(storedTeamName);
        // Clear the session storage to avoid using stale data
        sessionStorage.removeItem('teamName');
      } else {
        // In a real app, you would fetch the team details from an API
        // For now, we'll use a fixed team name
        setTeamName('AI Team');
      }
    };

    fetchTeamDetails();

    // Set current chat ID and favorite status
    setCurrentChatId(id || 'current-chat');

    // Find the current conversation and set its favorite status
    const currentConversation = conversations.find(conv => conv.id === (id || 'current-chat'));
    if (currentConversation) {
      setCurrentChatFavorite(currentConversation.isFavorite);
    } else {
      // If no conversation found, assume it's a new chat (not favorite)
      setCurrentChatFavorite(false);
    }
  }, [id, conversations]);

  // Handle creating a new chat
  const handleNewChat = () => {
    // Create a new conversation ID
    const newChatId = `new_${Date.now()}`;

    // Add the new chat to the conversations list
    const newChat = {
      id: newChatId,
      title: 'New chat',
      name: 'New chat',
      timestamp: new Date(),
      isFavorite: false,
    };

    // Update conversations list with the new chat at the top
    setConversations([newChat, ...conversations]);

    // Clear any existing messages
    clearMessages();

    // Reset input value
    setInputValue('');

    // Navigate to the new chat
    navigate(`/dashboard/agents/chat`);
  };

  // Handle selecting a conversation
  const handleSelectConversation = (conversationId: string) => {
    // In a real app, you would load the conversation from the database
    // For now, we'll just navigate to the conversation
    navigate(`/dashboard/agents/chat`);
  };

  // Handle toggling favorite status
  const handleToggleFavorite = () => {
    const newFavoriteStatus = !currentChatFavorite;
    setCurrentChatFavorite(newFavoriteStatus);

    // Update the conversation in the list
    setConversations(prevConversations =>
      prevConversations.map(conv =>
        conv.id === currentChatId
          ? { ...conv, isFavorite: newFavoriteStatus }
          : conv
      )
    );

    // In a real app, you would also update the database
    console.log(`Chat ${currentChatId} favorite status changed to:`, newFavoriteStatus);
  };

  // Calculate drawer width
  const drawerWidth = 340;

  return (
    <>
      <Helmet>
        <title>
          {t('pages.teams.chatWith')} {teamName} | Midad AI
        </title>
      </Helmet>

      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          height:'93vh',
          background: (theme) => theme.palette.background.default,
          overflow: 'hidden',
        }}
      >
        {/* Left Sidebar */}

        {/* Main Chat Column */}
        <Card
          component="main"
          sx={{
            flexGrow: 1,
            width: { xs: '100%', md: '60%' },
            display: 'flex',
            flexDirection: 'column',
            bgcolor: 'background.paper',
            borderRadius: { xs: 1, sm: 2 },
            border: '1px solid',
            borderColor: 'divider',
            order: { xs: 1, md: 0 },
          }}
        >
          {/* Code Editor Area */}

          {/* Chat header */}
          <Box
            sx={{
             
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              background: (theme) =>
                theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.8)' : 'white',
              backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(8px)' : 'none'),
              borderBottom: '1px solid',
              borderColor: 'divider',
              position: 'relative',
              zIndex: 5,
            }}
          >
            {/* Left side - Team info */}
            <Box sx={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
              <IconButton
                sx={{
                  mr: { xs: 0.5, sm: 1 },
                  p: { xs: 1, sm: 1.5 }
                }}
                onClick={() => navigate('/dashboard/teams')}
              >
                <Iconify icon="eva:arrow-ios-back-fill" width={isMobile ? 20 : 24} />
              </IconButton>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  flex: 1,
                  minWidth: 0,
                  '&:hover': {
                    opacity: 0.8,
                  },
                }}
                onClick={() => setMembersDialogOpen(true)}
              >
                <Box sx={{ minWidth: 0, flex: 1 }}>
                  <Typography
                    variant={isMobile ? 'body1' : 'subtitle1'}
                    sx={{
                      fontWeight: 'bold',
                      color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {teamName}
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Right side - Menu */}
         <Box
            sx={{
              p: 2,
              borderColor: 'divider',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              bgcolor: 'background.paper',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {isMobile && (
                <IconButton onClick={() => setDrawerOpen(!drawerOpen)}>
                  <Iconify icon="eva:menu-fill" width={24} />
                </IconButton>
              )}
             
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton
                size="small"
                onClick={handleToggleFavorite}
                sx={{
                  color: currentChatFavorite ? 'warning.main' : 'text.secondary',
                  '&:hover': {
                    color: 'warning.main',
                  },
                }}
              >
                <Iconify
                  icon={currentChatFavorite ? "eva:star-fill" : "eva:star-outline"}
                  width={20}
                />
              </IconButton>
              <IconButton onClick={()=>{setShareDialogOpen(true)}} size="small">
                <Iconify icon="eva:share-outline" width={20} />
              </IconButton>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                sx={{
                  width: { xs: 36, sm: 40 },
                  height: { xs: 36, sm: 40 },
                  p: { xs: 1, sm: 1.5 }
                }}
                onClick={(e) => setMenuAnchorEl(e.currentTarget)}
              >
                <Iconify icon="eva:more-vertical-fill" width={isMobile ? 20 : 24} />
              </IconButton>
              <Menu
                anchorEl={menuAnchorEl}
                open={Boolean(menuAnchorEl)}
                onClose={() => setMenuAnchorEl(null)}
              >
                <MenuItem
                  onClick={() => {
                    setMenuAnchorEl(null);
                    setShareDialogOpen(true);
                  }}
                >
                  <Iconify icon="eva:upload-fill" width={20} sx={{ mr: 1 }} />
                  Share
                </MenuItem>
                <MenuItem
                  onClick={() => {
                    setMenuAnchorEl(null);
                    handleToggleFavorite();
                  }}
                >
                  <Iconify
                    icon={currentChatFavorite ? "eva:star-fill" : "eva:star-outline"}
                    width={20}
                    sx={{
                      mr: 1,
                      color: currentChatFavorite ? 'warning.main' : 'inherit',
                    }}
                  />
                  {currentChatFavorite ? 'Remove from Favorites' : 'Add to Favorites'}
                </MenuItem>
                <Divider />
                <MenuItem sx={{ color: 'red' }} onClick={() => setMenuAnchorEl(null)}>
                  <Iconify icon="eva:close-circle-outline" width={20} sx={{ mr: 1 }} />
                  Delete
                </MenuItem>
              </Menu>
            </Box>
            </Box>
          </Box>
          </Box>

          {/* Chat component */}
          {messages.length === 0 ? (
            <Box
              sx={{
                flexGrow: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                p: { xs: 2, sm: 3 },
                position: 'relative',
                minHeight: { xs: 300, sm: 400 },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: (theme) =>
                    theme.palette.mode === 'dark'
                      ? 'radial-gradient(circle at center, rgba(255, 111, 60, 0.05) 0%, transparent 70%)'
                      : 'transparent',
                  zIndex: 0,
                },
              }}
            >
              {/* Simple Team Welcome */}
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                  maxWidth: { xs: 300, sm: 400 },
                  width: '100%',
                  position: 'relative',
                  zIndex: 1,
                  background: (theme) =>
                    theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.5)' : 'white',
                  backdropFilter: (theme) =>
                    theme.palette.mode === 'dark' ? 'blur(10px)' : 'none',
                  borderRadius: { xs: 2, sm: 3 },
                  p: { xs: 3, sm: 4 },
                }}
              >
                {/* Team Avatars */}
                <AvatarGroup max={4}>
                  {teamMembers.map((member) => (
                    <Avatar key={member.id} src={member.avatar} />
                  ))}
                  <Avatar
                    sx={{
                      bgcolor: (theme) =>
                        theme.palette.mode === 'dark'
                          ? alpha(theme.palette.primary.main, 0.2)
                          : '#FEE4DC',
                      color: '#FF6B35',
                    }}
                  >
                    +2
                  </Avatar>
                </AvatarGroup>

                {/* Team Name */}
                <Typography
                  variant={isMobile ? 'h6' : 'h5'}
                  sx={{
                    fontWeight: 'bold',
                    mb: { xs: 1, sm: 1.5 },
                    color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                  }}
                >
                  {teamName}
                </Typography>

                {/* Team Description */}
                <Typography
                  variant="body2"
                  sx={{
                    color: (theme) =>
                      theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : '#666',
                    textAlign: 'center',
                    fontSize: { xs: '0.8rem', sm: '0.9rem' },
                    maxWidth: { xs: 250, sm: 300 },
                    lineHeight: 1.5,
                  }}
                >
                  {t('pages.teams.emptyChat')}
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
              <TeamChat
                teamName={teamName}
                messages={messages}
                isTyping={isTyping}
                onSendMessage={handleSendMessage}
                onTabChange={setActiveTab}
                resultUrl="https://www.figma.com/embed?embed_host=share&url=https%3A%2F%2Fwww.figma.com%2Ffile%2FLKQ4FJ4bTnCSjedbRpk931%2FMaterial-Design-Component-Library%3Fnode-id%3D0%253A1"
              />
            </Box>
          )}

          {/* Message input - show in Conversation tab and when starting a new chat */}
          {(activeTab === 0 || messages.length === 0) && (
            <Box
              sx={{
                p: { xs: 1.5, sm: 2 },
                borderTop: '1px solid',
                borderColor: 'divider',
                display: 'flex',
                alignItems: 'center',
                background: (theme) =>
                  theme.palette.mode === 'dark'
                    ? 'linear-gradient(to top, rgba(30, 30, 30, 0.9) 0%, rgba(30, 30, 30, 0.8) 100%)'
                    : 'white',
                backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(8px)' : 'none'),
                position: 'relative',
                zIndex: 5,
              }}
            >
              <Box sx={{ position: 'relative', width: '100%' }}>
                <TextField
                  fullWidth
                  placeholder={isTyping ? '' : 'Message'}
                  variant="outlined"
                  size={isMobile ? 'small' : 'medium'}
                  value={isTyping ? '' : inputValue}
                  onChange={(e) => !isTyping && setInputValue(e.target.value)}
                  onKeyDown={(e) => {
                    if (!isTyping && e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      if (inputValue.trim()) {
                        handleSendMessage(inputValue);
                        setInputValue('');
                      }
                    }
                  }}
                  InputProps={{
                    startAdornment: isTyping ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                        <Iconify
                          icon="material-symbols:stars-2-rounded"
                          width="24"
                          height="24"
                          sx={{
                            color: (theme) =>
                              theme.palette.mode === 'dark' ? '#FF6B35' : '#FF6B35',
                            mr: 1,
                          }}
                        />
                        <Typography
                          variant="body2"
                          sx={{
                            color: (theme) =>
                              theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.7)'
                                : 'rgba(0, 0, 0, 0.6)',
                          }}
                        >
                          {t('common.agentThinking')}
                        </Typography>
                      </Box>
                    ) : null,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      bgcolor: (theme) =>
                        theme.palette.mode === 'dark'
                          ? alpha(theme.palette.common.white, 0.05)
                          : '#F5F5F5',
                      pr: 5, // Add padding for the button
                      '& fieldset': {
                        borderColor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.2)
                            : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.3)
                            : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: (theme) => theme.palette.primary.main,
                      },
                      '& input': {
                        color: (theme) =>
                          theme.palette.mode === 'dark' ? theme.palette.common.white : 'inherit',
                      },
                      '& input::placeholder': {
                        color: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.5)
                            : 'inherit',
                        opacity: 1,
                      },
                    },
                  }}
                />
                <IconButton
                  onClick={() => {
                    if (!isTyping && inputValue.trim()) {
                      handleSendMessage(inputValue);
                      setInputValue('');
                    }
                  }}
                  disabled={isTyping}
                  sx={{
                    position: 'absolute',
                    right: 8,
                    px:3,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    bgcolor: (theme) =>
                      isTyping
                        ? theme.palette.mode === 'dark'
                          ? 'rgba(255, 107, 53, 0.3)'
                          : '#FFD0BA'
                        : theme.palette.mode === 'dark'
                          ? alpha(theme.palette.primary.main, 0.9)
                          : 'transparent',
                    color: 'gray',
                    '&:hover': {
                      bgcolor: (theme) =>
                        isTyping
                          ? theme.palette.mode === 'dark'
                            ? 'rgba(255, 107, 53, 0.3)'
                            : '#FFD0BA'
                          : theme.palette.mode === 'dark'
                            ? theme.palette.primary.main
                            : '#E55A2A',
                    },
                    width: { xs: 28, sm: 32 },
                    height: { xs: 28, sm: 32 },
                    boxShadow: (theme) =>
                      theme.palette.mode === 'dark' && !isTyping
                        ? '0 0 8px rgba(255, 107, 53, 0.5)'
                        : 'none',
                    opacity: isTyping ? 0.7 : 1,
                    borderLeft: '1px solid rgba(24, 0, 72, 0.08)',
                    borderRadius:'0px'
                  }}
                >
                  <Iconify icon="eva:arrow-forward-fill" width={16} height={16} />
                </IconButton>
              </Box>
            </Box>
          )}
        </Card>
        {/* Results Column - Hidden on mobile */}
      
        {/* Chat History Column - Hidden on mobile */}
        <Card
          sx={{
            flexGrow: 1,
            ml:2,
            width: { xs: '100%', md: '5%'},
            flexDirection: 'column',
            bgcolor: 'background.paper',
            borderRadius: { xs: 1, sm: 2 },
            border: '1px solid',
            borderColor: 'divider',
            order: { xs: 3, md: 2 },
          }}
        >
          {/* Header */}
          <Box
            sx={{
              p: { xs: 1.5, sm:1 , md:2 },
              borderBottom: '1px solid',
              borderColor: 'divider',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              gap: 1,
            }}
          >
            <Typography
              fontWeight="bold"
              variant={isTablet ? 'subtitle1' : 'h6'}
              sx={{
                color: 'text.primary',
                minWidth: 0,
                flex: 1,
              }}
            >
              Chats History
            </Typography>

            <Button
              variant="contained"
              color="primary"
              size={isTablet ? 'small' : 'medium'}
              startIcon={<Iconify icon="mdi:comment-plus-outline" width={isTablet ? 16 : 20} />}
              onClick={handleNewChat}
              sx={{
                minWidth: { xs: 'auto', sm: 120 },
                borderRadius: 1,
                textTransform: 'none',
                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                px: { xs: 1.5, sm: 2 },
              }}
            >
              {isTablet ? 'New' : 'New Chat'}
            </Button>
          </Box>

          {/* Chat List */}
          <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
            <List>
              {conversations
                .sort((a, b) => {
                  // Sort favorites first, then by timestamp (newest first)
                  if (a.isFavorite && !b.isFavorite) return -1;
                  if (!a.isFavorite && b.isFavorite) return 1;
                  return b.timestamp.getTime() - a.timestamp.getTime();
                })
                .map((conversation) => (
                <ListItem key={conversation.id} disablePadding>
                  <ListItemButton
                    onClick={() => handleSelectConversation(conversation.id)}
                    selected={conversation.id === id}
                    sx={{
                      borderRadius: { xs: 1, sm: 2 },
                      bgcolor: 'background.paper',
                      p: { xs: 1.5, sm: 2 },
                      m: { xs: 0.5, sm: 1 },
                      boxShadow: 1,
                      '&.Mui-selected': {
                        bgcolor: 'action.selected',
                      },
                      '&:hover': {
                        bgcolor: 'action.hover',
                      },
                    }}
                  >
                    <ListItemText
                      disableTypography
                      primary={
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography
                              variant={isTablet ? 'body1' : 'subtitle1'}
                              sx={{
                                color: 'text.primary',
                                fontWeight: 'bold',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                                flex: 1,
                              }}
                            >
                              {conversation.title}
                            </Typography>
                            {conversation.isFavorite && (
                              <Iconify
                                icon="eva:star-fill"
                                width={16}
                                sx={{ color: 'warning.main', flexShrink: 0 }}
                              />
                            )}
                          </Box>
                          <Typography
                            variant={isTablet ? 'caption' : 'body2'}
                            sx={{
                              color: 'text.secondary',
                              mt: 0.5,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {conversation.name}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{
                              color: 'text.secondary',
                              mt: 0.5,
                              fontSize: { xs: '0.7rem', sm: '0.75rem' },
                            }}
                          >
                            {conversation?.timestamp?.toLocaleString()}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Box>
        </Card>
      </Box>

      {/* Share Dialog */}
      <Dialog
        open={shareDialogOpen}
        onClose={() => setShareDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        fullScreen={isSmallMobile}
        PaperProps={{
          sx: {
            backgroundColor: (theme) =>
              theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.9)' : '#F9F9F9',
            backdropFilter: 'blur(10px)',
            border: '1px solid',
            borderColor: (theme) =>
              theme.palette.mode === 'dark' ? 'rgba(255, 111, 60, 0.2)' : 'rgba(0, 0, 0, 0.1)',
            borderRadius: { xs: 0, sm: 2 },
            m: { xs: 0, sm: 2 },
          },
        }}
        >
        <DialogTitle sx={{
          fontWeight: 'bold',
          fontSize: { xs: '1.25rem', sm: '1.5rem' },
          p: { xs: 2, sm: 3 }
        }}>
          Share Chat
        </DialogTitle>
        <DialogContent sx={{ p: { xs: 2, sm: 3 } }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" component="div" sx={{ mb: 1 }}>
              Copy link <span style={{ color: 'red' }}>*</span>
            </Typography>
            <TextField
              fullWidth
              value={`${window.location.origin}/dashboard/teams/chat/${id}`}
              size="small"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <Button
                      startIcon={<Iconify icon="eva:copy-fill" />}
                      onClick={() =>
                        navigator.clipboard.writeText(
                          `${window.location.origin}/dashboard/teams/chat/${id}`
                        )
                      }
                      sx={{ color: 'primary.main',
                        borderLeft:'1px solid rgba(24, 0, 72, 0.08)',
                        
                       }}
                      >
                      Copy
                    </Button>
                  </InputAdornment>
                ),
              }}
              />
          </Box>

          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" component="div" sx={{ mb: 1 }}>
              Share with others <span style={{ color: 'red' }}>*</span>
            </Typography>
            <TextField
              fullWidth
              placeholder="Email address"
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:email-fill" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <Button
                      startIcon={<Iconify icon="eva:person-add-fill" />}
                      onClick={() => {
                        if (emailInput) {
                          setSharedEmails([...sharedEmails, emailInput]);
                          setEmailInput('');
                        }
                      }}
                      sx={{
                        color: 'primary.main',
                        borderLeft:'1px solid rgba(24, 0, 72, 0.08)'
                      }}
                    >
                      Invite
                    </Button>
                  </InputAdornment>
                ),
              }}
            />
          </Box>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
            {sharedEmails.map((email, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? 'rgba(255, 255, 255, 0.05)'
                      : 'rgba(0, 0, 0, 0.05)',
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Iconify icon="eva:person-fill" />
                  <Typography variant="body2">{email}</Typography>
                </Box>
                <IconButton
                  size="small"
                  onClick={() => {
                    setSharedEmails(sharedEmails.filter((_, i) => i !== index));
                  }}
                >
                  <Iconify icon="eva:close-circle-fill" />
                </IconButton>
              </Box>
            ))}
          </Box>
        </DialogContent>
        <DialogActions sx={{
          m: { xs: 2, sm: 3 },
          gap: 1,
          flexDirection: { xs: 'column', sm: 'row' },
        }}>
          <Button
            variant="outlined"
            onClick={() => setShareDialogOpen(false)}
            sx={{
              width: { xs: '100%', sm: 'auto' },
              minWidth: { sm: 79 },
              order: { xs: 2, sm: 1 }
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            sx={{
              width: { xs: '100%', sm: 'auto' },
              minWidth: { sm: 79 },
              order: { xs: 1, sm: 2 }
            }}
            onClick={() => {
              // Handle share logic here
              setShareDialogOpen(false);
            }}
          >
            Share
          </Button>
        </DialogActions>
      </Dialog>

      {/* Team Members Dialog */}
      <TeamMembersDialog
        open={membersDialogOpen}
        onClose={() => setMembersDialogOpen(false)}
        teamName={teamName}
        members={MOCK_TEAM_MEMBERS.map((member) => ({
          id: member.id,
          name: member.name,
          avatar: member.avatar,
          role: 'Social Media',
        }))}
      />
    </>
  );
}
