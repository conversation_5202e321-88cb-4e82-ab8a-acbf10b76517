import { AppTable } from 'src/components/table/app-table/app-table';
import useKnowledgeBaseTable, { FileType } from '../hooks/use-knowledge-base-table';

// ----------------------------------------------------------------------

export default function KnowledgeBaseTable() {
  const {
    files,
    selectedFiles,
    handleSelectFile,
    handleSelectAllFiles,
    table,
    columns,
    headLabels,
  } = useKnowledgeBaseTable();

  // Custom columns with proper rendering are defined in the hook

  // Custom select configuration
  const selectConfig = {
    idPath: 'id' as keyof FileType,
    handleSelectRow: (row: FileType, _: any) => handleSelectFile(row.id),
    handleSelectAllRows: (_: string[]) => (checked: boolean) => handleSelectAllFiles(checked),
    selected: selectedFiles,
    rowCount: files.length,
    numSelected: selectedFiles.length,
    selectedRowsActions: [],
  };

  return (
    <AppTable<FileType>
      headLabels={headLabels}
      data={files}
      columns={columns}
      dataCount={files.length}
      table={table}
      select={selectConfig}
      sx={{
        boxShadow: 'none',
        border: '1px solid rgba(224, 223, 226, 1)',
        '& .MuiTableCell-head': {
          bgcolor: 'transparent',
        },
        '& .MuiTableCell-root': {
          borderBottom: '1px solid #f0f0f0',
        },
      }}
    />
  );
}
