import { useState } from 'react';
import LongMenu from 'src/components/long-menu';
import { AppTablePropsType, useTable } from 'src/components/table';
import { fDate } from 'src/utils/format-time';

// Mock data for files
const FILES = [
  {
    id: '1',
    name: 'student_names.json',
    type: 'json',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '2',
    name: 'Presentation.pptx',
    type: 'pptx',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '3',
    name: 'My class.pdf',
    type: 'pdf',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '4',
    name: 'Mid-exam.docx',
    type: 'docx',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '5',
    name: 'Meeting Notes.txt',
    type: 'txt',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '6',
    name: 'student_names.json',
    type: 'json',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '7',
    name: 'Presentation.pptx',
    type: 'pptx',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '8',
    name: 'My class.pdf',
    type: 'pdf',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
  {
    id: '9',
    name: 'Mid-exam.docx',
    type: 'docx',
    size: '95 KB',
    date: new Date('2022-05-06'),
    usedBy: 5,
  },
];

export type FileType = {
  id: string;
  name: string;
  type: string;
  size: string;
  date: Date;
  usedBy: number;
};

export default function useKnowledgeBaseTable() {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDate, setSelectedDate] = useState('12 Jan 2024');
  const [selectedCategory, setSelectedCategory] = useState('Documents');

  // Table configuration
  const table = useTable({
    defaultRowsPerPage: 10,
    defaultDense: false,
    defaultOrderBy: 'name',
    defaultOrder: 'asc',
    defaultCurrentPage: 0,
    defaultSelected: [],
  });

  const handleSelectFile = (fileId: string) => {
    table.onSelectRow(fileId);
    const isSelected = selectedFiles.includes(fileId);
    if (isSelected) {
      setSelectedFiles(selectedFiles.filter((id) => id !== fileId));
    } else {
      setSelectedFiles([...selectedFiles, fileId]);
    }
  };

  const handleSelectAllFiles = (checked: boolean) => {
    const fileIds = FILES.map((file) => file.id);
    table.onSelectAllRows(checked, fileIds);
    if (checked) {
      setSelectedFiles(fileIds);
    } else {
      setSelectedFiles([]);
    }
  };

  const handleClearCategory = () => {
    setSelectedCategory('');
  };

  const MENU_OPTIONS = (_: FileType) => [
    {
      label: 'View',
      icon: 'eva:eye-fill',
      onClick: () => {},
      color: 'inherit',
    },
    {
      label: 'Edit',
      icon: 'eva:edit-fill',
      onClick: () => {},
      color: 'primary.main',
    },
    {
      label: 'Delete',
      icon: 'eva:trash-2-outline',
      onClick: () => {},
      color: 'error.main',
    },
  ];

  // Table columns configuration for AppTable
  const columns: AppTablePropsType<FileType>['columns'] = [
    {
      name: 'name',
      PreviewComponent: (data) => data.name,
    },
    {
      name: 'date',
      PreviewComponent: (data) => {
        if (!data?.date) return '';
        const dateValue = fDate(data.date);
        return dateValue || '';
      },
    },
    {
      name: 'size',
      PreviewComponent: (data) => data.size,
    },
    {
      name: 'usedBy',
      PreviewComponent: (data) => `Used by ${data.usedBy} agents`,
    },
    {
      name: 'id',
      cellSx: { width: '50px' },
      PreviewComponent: (row) => {
        return <LongMenu options={MENU_OPTIONS(row)} />;
      },
    },
  ];

  // Table head labels
  const headLabels = [
    { id: 'name', label: 'File Name' },
    { id: 'date', label: 'Upload Date' },
    { id: 'size', label: 'Size' },
    { id: 'usedBy', label: 'Users Count' },
    { id: 'actions', label: 'Actions' },
  ];

  return {
    files: FILES,
    selectedFiles,
    searchQuery,
    setSearchQuery,
    selectedDate,
    setSelectedDate,
    selectedCategory,
    handleSelectFile,
    handleSelectAllFiles,
    handleClearCategory,
    table,
    columns,
    headLabels,
  };
}
