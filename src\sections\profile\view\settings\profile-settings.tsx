import { <PERSON>, Stack, Typography, <PERSON><PERSON><PERSON>, Switch, Avatar } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

export function ProfileSettings() {
  const { t } = useTranslation();

  return (
    <Stack spacing={3}>
      <Typography variant="h4">Profile</Typography>

      {/* Profile Picture */}
      <Stack spacing={1}>
        <Typography variant="subtitle2">Profile Pictures</Typography>
        <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={2}>
          <Stack direction="column">
            <Stack direction="row" alignItems="center">
              <Avatar
                alt="Profile Picture"
                src="/path/to/your/image.jpg" 
                sx={{ width: 80, height: 80 }}
              />
              <Typography variant="caption" px="10px" color="text.secondary">
                At least 800x800 px recommended. JPG or PNG and GIF is allowed
              </Typography>
            </Stack>
          </Stack>

          <AppButton
            variant="outlined"
            component="label"
            label="Upload new image"
            sx={{width:'15%'}}
          >
            <input type="file" hidden />
          </AppButton>
        </Stack>
      </Stack>

      {/* Input Fields */}
      <Stack spacing={2}>
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('First Name')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              value="Ahmad"
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>

          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Last Name')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              value="Ahmad"
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>
        </Stack>

        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Username')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              value="@xpk.crtv"
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>

          <Box sx={{ mb: 2, flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Emails')}
            </Typography>
            <TextField
              fullWidth
              size="small"
              type="email"
              value="<EMAIL>"
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            />
          </Box>
        </Stack>
      </Stack>

      {/* Developer Mode */}
      <Stack spacing={1}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="subtitle1">Developer Mode</Typography>
          <Switch sx={{ mt: '35px' }} defaultChecked />
        </Stack>
        <Typography variant="body2" color="text.secondary">
          Enable the developer mode to use advanced tools like connecting a custom database.
        </Typography>

        <AppButton
          variant="contained"
          sx={{
            width: '100px',
            borderRadius: 1,
            alignSelf: 'flex-end',
            mt: 3,
            bgcolor: 'primary.main',
          }}
          label="Update"
        />
      </Stack>
    </Stack>
  );
}
