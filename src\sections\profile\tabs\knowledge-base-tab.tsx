import { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  LinearProgress,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  alpha,
  MenuItem,Stack,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { useTranslation } from 'react-i18next';
import { AppButton } from 'src/components/common';

import ConnectResourceDialog from '../components/connect-resource-dialog';
import DatabasesDialog from '../components/databases-dialog';
import APIsDialog from '../components/apis-dialog';
import KnowledgeBaseTable from '../components/knowledge-base-table';
import CloudPlatformsCarousel from '../components/cloud-platforms-carousel';
import useKnowledgeBaseTable from '../hooks/use-knowledge-base-table';
// import { Searchbar } from 'src/layouts/components/searchbar';

// ----------------------------------------------------------------------

// Mock data for cloud storage platforms
const CLOUD_PLATFORMS = [
  {
    id: 'google-drive',
    name: 'Google Drive',
    icon: '/assets/icons/platforms/ic_google_drive.svg',
    files: 561,
    color: '#FEE4DC',
    progress: 70,
  },
  {
    id: 'mega-drive',
    name: 'Mega Drive',
    icon: '/assets/icons/platforms/ic_mega.svg',
    files: 561,
    color: '#FFE8E5',
    progress: 40,
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    icon: '/assets/icons/platforms/ic_dropbox.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 50,
  },
  {
    id: 'one-drive',
    name: 'One Drive',
    icon: '/assets/icons/platforms/ic_onedrive.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 30,
  },
  {
    id: 'google-drive-2',
    name: 'Google Drive',
    icon: '/assets/icons/platforms/ic_google_drive.svg',
    files: 561,
    color: '#FEE4DC',
    progress: 70,
  },
  {
    id: 'mega-drive-2',
    name: 'Mega Drive',
    icon: '/assets/icons/platforms/ic_mega.svg',
    files: 561,
    color: '#FFE8E5',
    progress: 40,
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    icon: '/assets/icons/platforms/ic_dropbox.svg',
    files: 561,
    color: '#E0F8FF',
    progress: 50,
  },
];

// Mock data for connection options
const CONNECTION_OPTIONS = [
  {
    id: 'connect',
    title: 'Connect',
    description: 'Connect to a new cloud platform',
    icon: 'solar:link-outline',
    color: 'primary.main',
  },
  {
    id: 'databases',
    title: 'Databases',
    description: 'Connect to database',
    icon: 'solar:database-outline',
    color: 'primary.main',
  },
  {
    id: 'api',
    title: 'API',
    description: 'Connect to a custom API',
    icon: 'solar:code-square-outline',
    color: 'primary.main',
  },
];

// Mock data for file categories
const FILE_CATEGORIES = [
  {
    id: 'documents',
    title: 'Documents',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:file-text-fill',
    color: 'primary.main',
  },
  {
    id: 'pictures',
    title: 'Pictures',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:image-fill',
    color: 'primary.main',
  },
  {
    id: 'audio',
    title: 'Audio',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:music-fill',
    color: 'primary.main',
  },
  {
    id: 'videos',
    title: 'Videos',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:video-fill',
    color: 'primary.main',
  },
  {
    id: 'downloads',
    title: 'Downloads',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:download-fill',
    color: 'primary.main',
  },
  {
    id: 'trash',
    title: 'Trash',
    count: 60,
    size: '1.52 GB',
    icon: 'eva:trash-2-fill',
    color: 'primary.main',
  },
];

// File data is now in the useKnowledgeBaseTable hook

// ----------------------------------------------------------------------

export default function KnowledgeBaseTab() {
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);
  const [databasesDialogOpen, setDatabasesDialogOpen] = useState(false);
  const [apisDialogOpen, setApisDialogOpen] = useState(false);
  const { t } = useTranslation();
  const [fileType, setFileType] = useState('');
const value = 75;
  // State for selected cards
  const [selectedFileCategory, setSelectedFileCategory] = useState<string | null>(null);

  const {
    searchQuery,
    setSearchQuery,
    selectedDate,
    setSelectedDate,
    selectedCategory,
    handleClearCategory,
  } = useKnowledgeBaseTable();

  const handleOpenConnectDialog = () => {
    setConnectDialogOpen(true);
  };

  const handleCloseConnectDialog = () => {
    setConnectDialogOpen(false);
  };

  const handleOpenDatabasesDialog = () => {
    setDatabasesDialogOpen(true);
  };

  const handleCloseDatabasesDialog = () => {
    setDatabasesDialogOpen(false);
  };

  const handleOpenApisDialog = () => {
    setApisDialogOpen(true);
  };

  const handleCloseApisDialog = () => {
    setApisDialogOpen(false);
  };

  // Upload Button with progress indicator
  const UploadButton = () => {
    const [isUploading, setIsUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Reset upload state when component unmounts
    useEffect(() => {
      return () => {
        if (isUploading) {
          setIsUploading(false);
          setUploadProgress(0);
        }
      };
    }, [isUploading]);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;

      if (files && files.length > 0) {
        // Start upload simulation
        setIsUploading(true);
        setUploadProgress(0);

        // Simulate upload progress with setInterval
        const interval = setInterval(() => {
          setUploadProgress((prevProgress) => {
            const newProgress = prevProgress + 10;

            // When progress reaches 100%, clear interval and reset
            if (newProgress >= 100) {
              clearInterval(interval);

              // Reset after a short delay and show success message
              setTimeout(() => {
                setIsUploading(false);
                setUploadProgress(0);

                // Reset the file input
                if (fileInputRef.current) {
                  fileInputRef.current.value = '';
                }

                // Show success message (optional)
                console.log('Upload completed successfully!');
              }, 500);

              return 100;
            }

            return newProgress;
          });
        }, 300); // Update every 300ms for a total of ~3 seconds
      }
    };

    const handleButtonClick = () => {
      if (isUploading) return; // Prevent clicks during upload

      // Trigger the hidden file input
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    };

    return (
      <>
        {/* Hidden file input */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          style={{ display: 'none' }}
          multiple
        />

        {/* Upload button with progress */}
        <AppButton
          variant="contained"
          startIcon={isUploading ? null : <Iconify icon="eva:upload-fill" />}
          onClick={handleButtonClick}
          label={isUploading ? `Uploading ${uploadProgress}%` : 'Upload'}
          sx={{
            bgcolor: isUploading ? 'rgba(255, 111, 60, 0.08)' : 'primary.main',
            color: isUploading ? 'primary.main' : 'white',
            '&:hover': { bgcolor: isUploading ? 'rgba(255, 111, 60, 0.08)' : 'primary.main', },
            borderRadius: 1,
            alignSelf: { xs: 'flex-start', sm: 'center' },
            whiteSpace: 'nowrap',
            px: { xs: 2, sm: 3 },
            py: { xs: 0.75, sm: 1 },
            fontSize: { xs: '0.8125rem', sm: '0.875rem' },
            minWidth: 120,
            position: 'relative',
            '&.Mui-disabled': {
              bgcolor: 'primary.main',
              color: 'white',
              opacity: 0.8,
            },
          }}
        />
      </>
    );
  };

  // Connect to Cloud Button with progress indicator
  const ConnectToCloudButton = () => {
    const [isConnecting, setIsConnecting] = useState(false);
    const [connectProgress, setConnectProgress] = useState(0);

    // Reset connect state when component unmounts
    useEffect(() => {
      return () => {
        if (isConnecting) {
          setIsConnecting(false);
          setConnectProgress(0);
        }
      };
    }, [isConnecting]);

    

    return (
      <AppButton
        variant="outlined"
        startIcon={<Iconify icon="solar:link-outline" />}
        onClick={handleOpenConnectDialog}
        label='Connect to a cloud'
        sx={{
          bgcolor: isConnecting ? 'rgba(255, 111, 60, 0.08)' : 'transparent',
          color: isConnecting ? 'primary.main' : 'primary.main',
          borderColor: 'primary.main',
          '&:hover': {
            bgcolor: 'rgba(255, 111, 60, 0.08)',
            borderColor: 'primary.main'
          },
          borderRadius: 1,
          alignSelf: { xs: 'flex-start', sm: 'center' },
          whiteSpace: 'nowrap',
          px: { xs: 2, sm: 4 },
          py: { xs: 0.75, sm: 1 },
          fontSize: { xs: '0.8125rem', sm: '0.875rem' },
          minWidth: 160,
          position: 'relative',
          '&.Mui-disabled': {
            borderColor: 'primary.main',
            color: 'primary.main',
            opacity: 0.8,
          },
        }}
      />
    );
  };

  return (
    <Box
      sx={{
        width: '100%',
        px: { xs: 2, sm: 3, md: 4, lg: 5 },
        py: 3,
        backgroundColor: 'rgba(249, 249, 249, 1)',
        borderRadius: '32px',
      }}
    >
      {/* Connect Resource Dialog */}
      <ConnectResourceDialog open={connectDialogOpen} onClose={handleCloseConnectDialog} />

      {/* Databases Dialog */}
      <DatabasesDialog open={databasesDialogOpen} onClose={handleCloseDatabasesDialog} />

      {/* APIs Dialog */}
      <APIsDialog open={apisDialogOpen} onClose={handleCloseApisDialog} />

      {/* Header Section */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: 3,
          gap: { xs: 2, sm: 0 },
        }}
      >
        <Box sx={{ width: { xs: '100%', sm: 'auto' } }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: { xs: 'flex-start', sm: 'center' },
              flexDirection: { xs: 'column', sm: 'row' },
              gap: { xs: 1, sm: 2 },
              mb: 1,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton
                sx={{
                  mr: -1,
                  color: 'text.primary',
                  backgroundColor: 'rgba(242, 242, 242, 1)',
                  border: '1px solid rgba(242, 242, 242, 1)',
                  '&:hover': { bgcolor: 'transparent', border: '1px solid rgba(242, 242, 242, 1)' },
                }}
                onClick={() => window.history.back()}
              >
                <Iconify icon="eva:arrow-back-fill" width={24} />
              </IconButton>
              <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                Knowledge Base
              </Typography>
              <Chip
                label="DEVELOPER MODE"
                size="small"
                sx={{
                  background:
                    'radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.03)), radial-gradient(50% 50% at 50% 50%, rgba(177, 60, 255, 0.2) 18.63%, rgba(177, 60, 255, 0.19)), radial-gradient(50% 50% at 50% 50%, #425de3, #a021ea 85.15%), radial-gradient(50% 50% at 50% 50%, #ff41d6, #7100bc), #030303',
                  color: 'rgba(255, 255, 255, 1)',
                  fontWeight: 'bold',
                  fontSize: '0.7rem',
                  height: 24,
                  borderRadius: '40px',
                }}
              />
            </Box>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mx: 7 }}>
            Here you can upload your own files to help the agents to complete tasks
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <ConnectToCloudButton />
          <UploadButton />
        </Box>
      </Box>

      {/* Storage Progress */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 7, mb: 2 }}>
        <Box>
          <Typography sx={{ textWrap: 'nowrap', mx: 1 }} variant="subtitle2">
            Cloud storage
          </Typography>
        </Box>
        <Box sx={{ position: 'relative', width: '100%' }}>
          <LinearProgress
            variant="determinate"
            value={value}
            sx={{
              height: 24,
              borderRadius: 4,
              bgcolor: 'rgba(242, 242, 242, 1)',
              '& .MuiLinearProgress-bar': {
                bgcolor: 'primary.main',
                borderRadius: 4,
              },
            }}
          />
          <Typography
            variant="subtitle2"
            sx={{
              position: 'absolute',
              top: -2,
              left: 0,
              right: 0,
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
      color: value < 50 ? 'grey.700' : 'common.white',
            }}
          >
            17% (3.59 GB used)
          </Typography>
        </Box>
        <Box sx={{ mx: 1, display: 'flex', alignItems: 'center', textWrap: 'nowrap' }}>
          <Typography variant="subtitle2" sx={{ color: 'primary.main' }}>
            5 GB
          </Typography>
          <AppButton
            variant="text"
            size="small"
            sx={{ fontWeight: 'bold' }}
            label="Upgrade"
          />
        </Box>
      </Box>
      {/* Cloud Platforms */}
      <Box sx={{ mb: 4 }}>
        <CloudPlatformsCarousel platforms={CLOUD_PLATFORMS} />
      </Box>

      {/* Connection Options */}
      <Grid container spacing={2} sx={{ my: 2 }}>
        {CONNECTION_OPTIONS.map((option) => (
          <Grid item xs={12} sm={4} key={option.id}>
            <Card
              sx={{
                padding: '16px',
                height: '176px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                border: `2px dashed rgba(24, 0, 72, 0.24)`,
                boxShadow: 'none',
                cursor: 'pointer',
                borderRadius: '8px',

                color: 'inherit',
                '&:hover': {
                  borderColor: 'primary.main',
                },
              }}
              onClick={() => {
                if (option.id === 'connect') {
                  handleOpenConnectDialog();
                } else if (option.id === 'databases') {
                  handleOpenDatabasesDialog();
                } else if (option.id === 'api') {
                  handleOpenApisDialog();
                }
              }}
            >
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 2,
                  color: option.color,
                }}
              >
                <Iconify icon={option.icon} width={60} height={60} />
              </Box>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                {option.title}
              </Typography>
              <Typography variant="body2" align="center" sx={{ fontWeight: 'light' }}>
                {option.description}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* File Categories */}
      {/* <Grid container spacing={1} sx={{ my: 4 }}>
        {FILE_CATEGORIES.map((category) => (
          <Grid item xs={6} sm={4} md={2} key={category.id}>
            <Card
              sx={{
                p: 1,
                display: 'flex',
                height: '62px',
                flexDirection: 'column',
                alignItems: 'flex-start',
                boxShadow: 'none',
                border: '1px solid #f0f0f0',
                cursor: 'pointer',
                borderRadius: '8px',
                bgcolor:
                  selectedFileCategory === category.id
                    ? 'rgba(255, 226, 216, 1)'
                    : 'rgba(242, 242, 242, 1)',
                color: selectedFileCategory === category.id ? 'white' : 'inherit',
                '&:hover': {
                  borderColor: '#primary.lighter',
                },
                '& .MuiTypography-caption': {
                  color:
                    selectedFileCategory === category.id
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'text.secondary',
                },
              }}
              onClick={() => setSelectedFileCategory(category.id)}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: 1,
                    color: category.color,
                    mr: 1,
                  }}
                >
                  <Iconify icon={category.icon} width={40} height={40} />
                </Box>
                <Box>
                  <Typography color="rgba(0, 0, 0, 1)" variant="subtitle1">
                    {category.title}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'rgba(0, 0, 0, 0.72)' }}>
                    {category.count} files • {category.size}
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid> */}

      {/* Files Table Section */}
      <Box sx={{ mb: 3 }}>
        {/* Table Filters */}
        <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 2 }}>
          {/* 1. File Type */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('File Type')}
            </Typography>
            <TextField
              select
              fullWidth
              size="small"
              value={fileType}
              onChange={(e) => setFileType(e.target.value)}
              InputProps={{
                sx: { borderRadius: 1 },
              }}
            >
              <MenuItem value="pdf">PDF</MenuItem>
              <MenuItem value="docx">DOCX</MenuItem>
              <MenuItem value="xlsx">XLSX</MenuItem>
            </TextField>
          </Box>

          {/* 2. Date Picker */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Date')}
            </Typography>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                value={new Date(selectedDate)}
                onChange={(newDate) => {
                  if (newDate) {
                    const formattedDate = new Intl.DateTimeFormat('en-US', {
                      day: 'numeric',
                      month: 'short',
                      year: 'numeric',
                    }).format(newDate);
                    setSelectedDate(formattedDate);
                  }
                }}
                
                 slotProps={{
        textField: {
          size: 'small',
          fullWidth: true,
          InputProps: { sx: { borderRadius: 1 } },
          sx: {
            '& .MuiSvgIcon-root': {
              color: 'primary.main', 
            },
          },
        },
        openPickerButton: { size: 'small' },
      }}
              />
            </LocalizationProvider>
          </Box>

          {/* 3. Search Field */}
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" component="div" sx={{ mb: 1 }}>
              {t('Search')}
            </Typography>
            <TextField
              placeholder="Search..."
              size="small"
              fullWidth
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify
                      icon="eva:search-fill"
                      width={20}
                      sx={{
                        color: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.7)
                            : 'text.disabled',
                      }}
                    />
                  </InputAdornment>
                ),
                sx: { borderRadius: 1 },
              }}
            />
          </Box>
        </Stack>

        {/* Results Count and Category Filter */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {' 30 '}
          <Typography variant="body2" sx={{ mx: 1, color: 'text.secondary' }}>
            results found
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2">Category:</Typography>
            {selectedCategory && (
              <Chip
                label={selectedCategory}
                onDelete={handleClearCategory}
                size="small"
                sx={{
                  bgcolor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.common.white, 0.08)
                      : '#f5f5f5',
                  color: (theme) =>
                    theme.palette.mode === 'dark' ? theme.palette.common.white : 'black',
                  '& .MuiChip-deleteIcon': {
                    color: (theme) =>
                      theme.palette.mode === 'dark' ? theme.palette.common.white : 'black',
                    '&:hover': {
                      color: (theme) =>
                        theme.palette.mode === 'dark'
                          ? alpha(theme.palette.common.white, 0.7)
                          : alpha(theme.palette.common.black, 0.7),
                    },
                  },
                }}
              />
            )}

            <Box
              sx={{
                display: 'flex',
                gap: '5px',
                alignItems: 'center',
                ml: { xs: 0, sm: '10px' },
                cursor: 'pointer',
              }}
              onClick={handleClearCategory}
            >
              <Iconify
                width="20px"
                height="20px"
                icon="material-symbols:delete-outline"
                sx={{
                  color: (theme) => (theme.palette.mode === 'dark' ? '#FF7A59' : '#FF5630'),
                }}
              />
              <Typography
                color="error"
                sx={{
                  fontSize: '0.875rem',
                  fontWeight: 'medium',
                }}
              >
                Clear
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Files Table */}
        <KnowledgeBaseTable />
      </Box>
    </Box>
  );
}
