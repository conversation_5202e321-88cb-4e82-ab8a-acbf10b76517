import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Button,
  IconButton,
  Stack,
  styled,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import ConnectDatabaseDialog from './connect-database-dialog';

// ----------------------------------------------------------------------

const StyledDatabaseItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(2),
  backgroundColor: theme.palette.background.neutral,
}));

const DisconnectButton = styled(Button)(({ theme }) => ({
  backgroundColor: 'rgba(255, 86, 48, 0.08)',
  color: theme.palette.error.main,
  '&:hover': {
    backgroundColor: 'rgba(255, 86, 48, 0.16)',
  },
}));

// ----------------------------------------------------------------------

type Database = {
  id: string;
  name: string;
  type: string;
  icon: string;
};

type DatabasesDialogProps = {
  open: boolean;
  onClose: () => void;
};

export default function DatabasesDialog({ open, onClose }: DatabasesDialogProps) {
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);

  // Mock data for connected databases
  const CONNECTED_DATABASES: Database[] = [
    {
      id: 'db-1',
      name: 'Students Data',
      type: 'MySQL',
      icon: 'devicon:mysql',
    },
    {
      id: 'db-2',
      name: 'Restaurant Data',
      type: 'MongoDB',
      icon: 'vscode-icons:file-type-mongo',
    },
    {
      id: 'db-3',
      name: 'Cafe Data',
      type: 'PostgreSQL',
      icon: 'devicon:postgresql',
    },
  ];

  const handleDisconnect = (databaseId: string) => {
    console.log(`Disconnecting database ${databaseId}`);
    // Implement disconnect logic here
  };

  const handleOpenConnectDialog = () => {
    setConnectDialogOpen(true);
  };

  const handleCloseConnectDialog = () => {
    setConnectDialogOpen(false);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        PaperProps={{
          sx: {
            borderRadius: 4,
            width: { xs: '90%', sm: '600px' },
            mx: 'auto',
          },
        }}
      >
        {/* Close button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 16,
            top: 16,
            color: 'grey.500',
            bgcolor: 'grey.100',
            '&:hover': {
              bgcolor: 'grey.200',
            },
            width: 36,
            height: 36,
            zIndex: 1,
          }}
        >
          <Iconify icon="eva:close-fill" width={20} height={20} />
        </IconButton>

        {/* Dialog content */}
        <DialogTitle sx={{ textAlign: 'center', pt: 4, pb: 2 }}>
          <Typography variant="h3" component="div">
            Databases
          </Typography>
        </DialogTitle>

        <DialogContent>
          <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
            Here&apos;s a list of your connected databases and you can add more
          </Typography>

          {CONNECTED_DATABASES.map((database) => (
            <StyledDatabaseItem key={database.id}>
              <Stack direction="row" spacing={2} alignItems="center">
                <Iconify icon={database?.icon} width="40px" height="40px" />

                <Box>
                  <Typography variant="subtitle1">{database.name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {database.type}
                  </Typography>
                </Box>
              </Stack>
              <DisconnectButton
                onClick={() => handleDisconnect(database.id)}
                variant="outlined"
                size="small"
              >
                Disconnect
              </DisconnectButton>
            </StyledDatabaseItem>
          ))}
        </DialogContent>

        <DialogActions
          sx={{
            width: '100%',
            justifyContent: 'end',
            background: (theme) => theme.palette.background.neutral,
            mt: '24px',

            borderTop: '1px solid #E1DFE4',
          }}
        >
          <AppButton
            variant="outlined"
            color="inherit"
            onClick={onClose}
            label="Cancel"
            size="small"
            fullWidth={false}
          />
          <AppButton
            variant="contained"
            color="primary"
            onClick={handleOpenConnectDialog}
            label="Add New"
            size="small"
            fullWidth={false}
          />
        </DialogActions>
      </Dialog>

      {/* Connect Database Dialog */}
      <ConnectDatabaseDialog open={connectDialogOpen} onClose={handleCloseConnectDialog} />
    </>
  );
}
